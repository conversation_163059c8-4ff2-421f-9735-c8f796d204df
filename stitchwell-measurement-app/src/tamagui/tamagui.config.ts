import { createFont } from '@tamagui/core';
import { createMedia } from '@tamagui/react-native-media-driver';
import { shorthands } from '@tamagui/shorthands';
import { themes, tokens } from '@tamagui/themes';
import { createTamagui } from 'tamagui';

// Create custom Poppins font configuration
const poppinsFont = createFont({
  family: 'Poppins',
  size: {
    1: 11,
    2: 12,
    3: 13,
    4: 14,
    true: 14,
    5: 16,
    6: 18,
    7: 20,
    8: 23,
    9: 30,
    10: 46,
    11: 55,
    12: 62,
    13: 72,
    14: 92,
    15: 114,
    16: 134,
  },
  lineHeight: {
    1: 17,
    2: 22,
    3: 25,
    4: 20,
    true: 20,
    5: 24,
    6: 26,
    7: 28,
    8: 32,
    9: 36,
    10: 52,
    11: 61,
    12: 68,
    13: 78,
    14: 98,
    15: 120,
    16: 140,
  },
  weight: {
    1: '300', // Light
    2: '400', // Regular
    3: '500', // Medium
    4: '600', // SemiBold
    5: '700', // Bold
    6: '800', // ExtraBold
    7: '900', // Black
  },
  letterSpacing: {
    1: 0,
    2: -0.5,
    3: -1,
    4: -1.5,
    5: -2,
    6: -2.5,
    7: -3,
    8: -4,
    9: -5,
    10: -6,
    11: -7,
    12: -8,
    13: -9,
    14: -10,
    15: -12,
    16: -14,
  },
  face: {
    300: { normal: 'Poppins-Light' },
    400: { normal: 'Poppins-Regular' },
    500: { normal: 'Poppins-Medium' },
    700: { normal: 'Poppins-Bold' },
  },
});

const appConfig = createTamagui({
  defaultTheme: 'light',
  shouldAddPrefersColorThemes: false,
  themeClassNameOnRoot: false,
  shorthands,
  fonts: {
    heading: poppinsFont,
    body: poppinsFont,
    display: poppinsFont,
  },
  themes,
  tokens,
  media: createMedia({
    xs: { maxWidth: 660 },
    sm: { maxWidth: 800 },
    md: { maxWidth: 1020 },
    lg: { maxWidth: 1280 },
    xl: { maxWidth: 1420 },
    xxl: { maxWidth: 1600 },
    gtXs: { minWidth: 660 + 1 },
    gtSm: { minWidth: 800 + 1 },
    gtMd: { minWidth: 1020 + 1 },
    gtLg: { minWidth: 1280 + 1 },
    short: { maxHeight: 820 },
    tall: { minHeight: 820 },
    hoverNone: { hover: 'none' },
    pointerCoarse: { pointer: 'coarse' },
  }),
});

export type AppConfig = typeof appConfig;

declare module 'tamagui' {
  interface TamaguiCustomConfig extends AppConfig {}
}

export default appConfig;
