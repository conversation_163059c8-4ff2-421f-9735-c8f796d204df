import React from 'react';
import { useFonts } from 'expo-font';
import { TamaguiProvider as TamaguiProviderOG } from 'tamagui';
import config from './tamagui.config';
import { View, ActivityIndicator } from 'react-native';

export function TamaguiProvider({ children }: { children: React.ReactNode }) {
  const [loaded] = useFonts({
    'Poppins-Light': require('../../assets/fonts/Poppins-Light.ttf'),
    'Poppins-Regular': require('../../assets/fonts/Poppins-Regular.ttf'),
    'Poppins-Medium': require('../../assets/fonts/Poppins-Medium.ttf'),
    'Poppins-Bold': require('../../assets/fonts/Poppins-Bold.ttf'),
  });

  if (!loaded) {
    return (
      <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
        <ActivityIndicator size="large" color="#007AFF" />
      </View>
    );
  }

  return (
    <TamaguiProviderOG config={config} disableInjectCSS defaultTheme="light">
      {children}
    </TamaguiProviderOG>
  );
}
