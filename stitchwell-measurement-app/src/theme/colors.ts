// Modern color palette with a more refined aesthetic
export const colors = {
  // Primary colors - Elegant blue gradient
  primary: '#3A86FF', // Vibrant blue
  primaryDark: '#2667FF',
  primaryLight: '#60A5FA',
  primaryGradient: ['#3A86FF', '#60A5FA'],

  // Secondary colors - Soft purple accent
  secondary: '#8B5CF6', // Soft purple
  secondaryDark: '#7C3AED',
  secondaryLight: '#A78BFA',
  secondaryGradient: ['#8B5CF6', '#A78BFA'],

  // Accent colors - Complementary to primary
  accent: '#10B981', // Emerald green
  accentDark: '#059669',
  accentLight: '#34D399',
  accentGradient: ['#10B981', '#34D399'],

  // Neutral colors - Clean and modern
  background: '#F9FAFB', // Very light gray
  backgroundAlt: '#F3F4F6', // Slightly darker background for contrast
  card: '#FFFFFF',
  cardAlt: '#F9FAFB', // Slightly off-white for secondary cards

  // Text colors - Improved readability
  text: '#111827', // Near black
  textSecondary: '#4B5563', // Dark gray
  textTertiary: '#9CA3AF', // Medium gray
  textLight: '#F9FAFB', // Light text for dark backgrounds

  // Status colors - Vibrant but not harsh
  success: '#10B981', // Emerald green
  warning: '#F59E0B', // Amber
  error: '#EF4444', // Red
  info: '#3B82F6', // Blue

  // UI elements - Subtle and refined
  border: '#E5E7EB',
  borderFocus: '#3A86FF',
  divider: '#E5E7EB',
  disabled: '#D1D5DB',
  placeholder: '#9CA3AF',

  // Specific UI components
  fabBackground: '#3A86FF',
  fabShadow: 'rgba(59, 130, 246, 0.5)',

  // Status-specific colors
  paid: '#10B981',
  pending: '#F59E0B',
  overdue: '#EF4444',

  // Card gradients
  cardGradient1: ['#3A86FF', '#60A5FA'],
  cardGradient2: ['#8B5CF6', '#A78BFA'],
  cardGradient3: ['#10B981', '#34D399'],
};

// Shadow styles for different elevations - more refined and subtle
export const shadows = {
  // Subtle shadow for small elements
  small: {
    shadowColor: 'rgba(0, 0, 0, 0.05)',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 1,
    shadowRadius: 2,
    elevation: 2,
  },
  // Medium shadow for cards and interactive elements
  medium: {
    shadowColor: 'rgba(0, 0, 0, 0.07)',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 1,
    shadowRadius: 5,
    elevation: 4,
  },
  // Pronounced shadow for elevated content
  large: {
    shadowColor: 'rgba(0, 0, 0, 0.09)',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 1,
    shadowRadius: 12,
    elevation: 8,
  },
  // Special shadow for floating action buttons
  button: {
    shadowColor: 'rgba(59, 130, 246, 0.5)', // Blue shadow matching primary color
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 1,
    shadowRadius: 8,
    elevation: 6,
  },
  // Subtle inset shadow for pressed states
  inset: {
    shadowColor: 'rgba(0, 0, 0, 0.05)',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 1,
    shadowRadius: 1,
    elevation: 1,
  },
};

// Border radius values for a modern, clean look
export const borderRadius = {
  small: 6,
  medium: 12,
  large: 16,
  extraLarge: 24,
  pill: 999,
  circle: 9999,
  // Special values for specific UI elements
  card: 16,
  button: 12,
  input: 10,
  tag: 8,
};

// Spacing values for consistent layout with 8-point grid system
export const spacing = {
  // Base spacing units
  '0': 0,
  '1': 4,
  '2': 8,
  '3': 12,
  '4': 16,
  '5': 20,
  '6': 24,
  '8': 32,
  '10': 40,
  '12': 48,
  '16': 64,
  '20': 80,
  '24': 96,

  // Named aliases for common uses
  xs: 4,
  sm: 8,
  md: 16,
  lg: 24,
  xl: 32,
  xxl: 48,

  // Specific spacing for different UI elements
  cardPadding: 16,
  sectionGap: 24,
  listItemGap: 12,
  inputPadding: 12,
  buttonPadding: 16,
};
