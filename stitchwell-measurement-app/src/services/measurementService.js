import {
  collection,
  addDoc,
  updateDoc,
  deleteDoc,
  doc,
  getDocs,
  getDoc,
  query,
  where,
  orderBy,
  onSnapshot,
  limit,
  startAfter
} from 'firebase/firestore';
import { db } from '../../firebaseConfig';

const MEASUREMENTS_COLLECTION = 'measurements';

/**
 * Get all measurements from Firestore
 * @returns {Promise<Array>} Array of measurement objects
 */
export const getAllMeasurements = async () => {
  try {
    const measurementsRef = collection(db, MEASUREMENTS_COLLECTION);
    // Try to get measurements ordered by date, but handle if date field doesn't exist
    let q;
    try {
      q = query(measurementsRef, orderBy('date', 'desc'));
    } catch (err) {
      console.warn('Could not order by date, using default order:', err);
      q = query(measurementsRef);
    }

    const querySnapshot = await getDocs(q);

    const measurements = [];
    querySnapshot.forEach((doc) => {
      try {
        const data = doc.data();
        // Ensure required fields exist
        const measurement = {
          id: doc.id,
          customerName: data.customerName || 'Unknown Customer',
          contactNumber: data.contactNumber || '',
          date: data.date || new Date().toISOString().split('T')[0],
          measurements: data.measurements || {},
          notes: data.notes || ''
        };

        measurements.push(measurement);
      } catch (docError) {
        console.error('Error processing document:', docError);
        // Continue with next document instead of failing the whole operation
      }
    });

    return measurements;
  } catch (error) {
    console.error('Error getting measurements:', error);
    throw error;
  }
};

/**
 * Get a single measurement by ID
 * @param {string} id - Measurement document ID
 * @returns {Promise<Object>} Measurement object
 */
export const getMeasurementById = async (id) => {
  try {
    const docRef = doc(db, MEASUREMENTS_COLLECTION, id);
    const docSnap = await getDoc(docRef);

    if (docSnap.exists()) {
      const data = docSnap.data();

      // Ensure required fields exist
      return {
        id: docSnap.id,
        customerName: data.customerName || 'Unknown Customer',
        contactNumber: data.contactNumber || '',
        date: data.date || new Date().toISOString().split('T')[0],
        measurements: data.measurements || {},
        notes: data.notes || ''
      };
    } else {
      throw new Error('Measurement not found');
    }
  } catch (error) {
    console.error('Error getting measurement:', error);
    throw error;
  }
};

/**
 * Search measurements by customer name
 * @param {string} searchTerm - Search term to match against customer name
 * @returns {Promise<Array>} Array of matching measurement objects
 */
export const searchMeasurementsByName = async (searchTerm) => {
  try {
    // Since Firestore doesn't support direct substring queries,
    // we'll fetch all measurements and filter client-side
    const measurements = await getAllMeasurements();

    if (!searchTerm || searchTerm.trim() === '') {
      return measurements;
    }

    const normalizedSearchTerm = searchTerm.toLowerCase().trim();

    return measurements.filter(measurement => {
      const customerName = (measurement.customerName || '').toLowerCase();
      const contactNumber = measurement.contactNumber || '';

      return customerName.includes(normalizedSearchTerm) ||
             contactNumber.includes(normalizedSearchTerm);
    });
  } catch (error) {
    console.error('Error searching measurements:', error);
    throw error;
  }
};

/**
 * Add a new measurement to Firestore
 * @param {Object} measurementData - Measurement data object
 * @returns {Promise<string>} ID of the newly created measurement
 */
export const addMeasurement = async (measurementData) => {
  try {
    const measurementsRef = collection(db, MEASUREMENTS_COLLECTION);
    const docRef = await addDoc(measurementsRef, {
      ...measurementData,
      date: measurementData.date || new Date().toISOString().split('T')[0]
    });

    return docRef.id;
  } catch (error) {
    console.error('Error adding measurement:', error);
    throw error;
  }
};

/**
 * Update an existing measurement
 * @param {string} id - Measurement document ID
 * @param {Object} measurementData - Updated measurement data
 * @returns {Promise<void>}
 */
export const updateMeasurement = async (id, measurementData) => {
  try {
    const docRef = doc(db, MEASUREMENTS_COLLECTION, id);
    await updateDoc(docRef, measurementData);
  } catch (error) {
    console.error('Error updating measurement:', error);
    throw error;
  }
};

/**
 * Delete a measurement
 * @param {string} id - Measurement document ID
 * @returns {Promise<void>}
 */
export const deleteMeasurement = async (id) => {
  try {
    const docRef = doc(db, MEASUREMENTS_COLLECTION, id);
    await deleteDoc(docRef);
  } catch (error) {
    console.error('Error deleting measurement:', error);
    throw error;
  }
};

/**
 * Subscribe to real-time updates of measurements with pagination
 * @param {Function} callback - Function to call with updated measurements and pagination info
 * @param {number} pageSize - Number of items to load per page
 * @param {Function} onError - Function to call on error
 * @returns {{ unsubscribe: Function, loadMore: Function }} Object containing unsubscribe function and loadMore function
 */
export const subscribeMeasurements = (callback, pageSize = 10, onError = () => {}) => {
  try {
    const measurementsRef = collection(db, MEASUREMENTS_COLLECTION);

    // Create a query ordered by date descending
    let baseQuery;
    try {
      baseQuery = query(measurementsRef, orderBy('date', 'desc'));
    } catch (err) {
      console.warn('Could not order by date, using default order:', err);
      baseQuery = query(measurementsRef);
    }

    // Add limit to the query
    const limitedQuery = query(baseQuery, limit(pageSize));

    // Keep track of the last document for pagination
    let lastVisibleDoc = null;
    let isLoading = false;
    let allLoaded = false;
    let currentItems = [];

    // Set up real-time listener for the first batch
    const unsubscribe = onSnapshot(limitedQuery, (querySnapshot) => {
      try {
        if (querySnapshot.empty) {
          allLoaded = true;
          callback([], { allLoaded });
          return;
        }

        const measurements = [];
        querySnapshot.forEach((doc) => {
          try {
            const data = doc.data();
            // Ensure required fields exist
            const measurement = {
              id: doc.id,
              customerName: data.customerName || 'Unknown Customer',
              contactNumber: data.contactNumber || '',
              date: data.date || new Date().toISOString().split('T')[0],
              measurements: data.measurements || {},
              notes: data.notes || ''
            };

            measurements.push(measurement);
          } catch (docError) {
            console.error('Error processing document:', docError);
            // Continue with next document instead of failing the whole operation
          }
        });

        // Update the last visible document for pagination
        lastVisibleDoc = querySnapshot.docs[querySnapshot.docs.length - 1];

        // Update current items
        currentItems = measurements;

        // Call the callback with the updated measurements and pagination info
        callback(measurements, {
          allLoaded: measurements.length < pageSize,
          isLoading: false
        });
      } catch (error) {
        console.error('Error processing snapshot:', error);
        onError(error);
      }
    }, (error) => {
      console.error('Error listening to measurements:', error);
      onError(error);
    });

    // Function to load more items
    const loadMore = async () => {
      // If already loading, all loaded, or no last document, don't proceed
      if (isLoading || allLoaded || !lastVisibleDoc) {
        return;
      }

      isLoading = true;
      callback(currentItems, { isLoading: true, allLoaded });

      try {
        // Create a query starting after the last visible document
        const nextQuery = query(
          baseQuery,
          startAfter(lastVisibleDoc),
          limit(pageSize)
        );

        const querySnapshot = await getDocs(nextQuery);

        if (querySnapshot.empty) {
          allLoaded = true;
          callback(currentItems, { isLoading: false, allLoaded });
          return;
        }

        const newMeasurements = [];
        querySnapshot.forEach((doc) => {
          try {
            const data = doc.data();
            const measurement = {
              id: doc.id,
              customerName: data.customerName || 'Unknown Customer',
              contactNumber: data.contactNumber || '',
              date: data.date || new Date().toISOString().split('T')[0],
              measurements: data.measurements || {},
              notes: data.notes || ''
            };

            newMeasurements.push(measurement);
          } catch (docError) {
            console.error('Error processing document:', docError);
          }
        });

        // Update the last visible document
        lastVisibleDoc = querySnapshot.docs[querySnapshot.docs.length - 1];

        // Combine current items with new items
        currentItems = [...currentItems, ...newMeasurements];

        // Check if we've loaded all items
        allLoaded = newMeasurements.length < pageSize;

        // Call the callback with the updated measurements
        callback(currentItems, {
          isLoading: false,
          allLoaded
        });
      } catch (error) {
        console.error('Error loading more measurements:', error);
        onError(error);
      } finally {
        isLoading = false;
      }
    };

    // Return both the unsubscribe function and the loadMore function
    return {
      unsubscribe,
      loadMore
    };
  } catch (error) {
    console.error('Error setting up measurements listener:', error);
    onError(error);
    // Return no-op functions in case of error
    return {
      unsubscribe: () => {},
      loadMore: () => {}
    };
  }
};
