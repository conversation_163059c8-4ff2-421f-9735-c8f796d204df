import React, { useEffect } from 'react';
import { TouchableOpacity, StyleSheet, ViewStyle, Animated, View } from 'react-native';
import { Text } from 'tamagui';
import { PlusIcon } from './TabIcons';
import { colors, shadows, borderRadius } from '../theme/colors';
import { LinearGradient } from 'expo-linear-gradient';

interface FloatingActionButtonProps {
  onPress: () => void;
  icon?: React.ReactNode;
  style?: ViewStyle;
}

const FloatingActionButton: React.FC<FloatingActionButtonProps> = ({
  onPress,
  icon,
  style,
}) => {
  // Animation for scale effect on press
  const scaleAnim = React.useRef(new Animated.Value(1)).current;

  // Animation for continuous subtle floating effect
  const floatAnim = React.useRef(new Animated.Value(0)).current;

  // Set up continuous floating animation
  useEffect(() => {
    const startFloatingAnimation = () => {
      Animated.sequence([
        Animated.timing(floatAnim, {
          toValue: 1,
          duration: 1500,
          useNativeDriver: true,
        }),
        Animated.timing(floatAnim, {
          toValue: 0,
          duration: 1500,
          useNativeDriver: true,
        })
      ]).start(() => startFloatingAnimation());
    };

    startFloatingAnimation();

    return () => {
      // Clean up animation when component unmounts
      floatAnim.stopAnimation();
    };
  }, []);

  const handlePressIn = () => {
    Animated.spring(scaleAnim, {
      toValue: 0.92,
      useNativeDriver: true,
      speed: 100,
      bounciness: 5,
    }).start();
  };

  const handlePressOut = () => {
    Animated.spring(scaleAnim, {
      toValue: 1,
      useNativeDriver: true,
      speed: 50,
      bounciness: 3,
    }).start();
  };

  // Calculate the floating translation based on the animation value
  const floatTranslateY = floatAnim.interpolate({
    inputRange: [0, 1],
    outputRange: [0, -6], // Subtle 6px up and down movement
  });

  return (
    <Animated.View
      style={[
        {
          transform: [
            { scale: scaleAnim },
            { translateY: floatTranslateY }
          ]
        },
        styles.container
      ]}
    >
      <TouchableOpacity
        style={[styles.buttonContainer, style]}
        onPress={onPress}
        activeOpacity={0.9}
        onPressIn={handlePressIn}
        onPressOut={handlePressOut}
      >
        <LinearGradient
          colors={colors.primaryGradient}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
          style={styles.gradient}
        >
          <View style={styles.iconContainer}>
            {icon || <PlusIcon size={30} color="white" />}
          </View>
        </LinearGradient>
      </TouchableOpacity>
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    bottom: 25,
    right: 25,
    zIndex: 999,
  },
  buttonContainer: {
    width: 64,
    height: 64,
    borderRadius: borderRadius.circle,
    overflow: 'hidden',
    ...shadows.button,
  },
  gradient: {
    width: '100%',
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
  },
  iconContainer: {
    width: '100%',
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
  }
});

export default FloatingActionButton;
