import React from 'react';
import { View, Animated } from 'react-native';
import { Card, Text, XStack, YStack } from 'tamagui';
import { ChartIcon, TrendingUpIcon } from './TabIcons';
import GradientWrapper from './GradientWrapper';
import { colors, shadows, borderRadius, spacing } from '../theme/colors';

interface StatisticsCardProps {
  totalCount: number;
  isLoading?: boolean;
}

const StatisticsCard: React.FC<StatisticsCardProps> = ({ 
  totalCount, 
  isLoading = false 
}) => {
  const animatedValue = React.useRef(new Animated.Value(0)).current;

  React.useEffect(() => {
    Animated.spring(animatedValue, {
      toValue: 1,
      useNativeDriver: true,
      tension: 100,
      friction: 8,
    }).start();
  }, []);

  const animatedStyle = {
    transform: [
      {
        scale: animatedValue.interpolate({
          inputRange: [0, 1],
          outputRange: [0.95, 1],
        }),
      },
    ],
    opacity: animatedValue,
  };

  return (
    <Animated.View style={[animatedStyle, { marginHorizontal: spacing.lg, marginBottom: spacing.md }]}>
      <Card
        elevate
        bordered={false}
        backgroundColor={colors.card}
        borderRadius={borderRadius.large}
        style={{
          ...shadows.medium,
          overflow: 'hidden',
        }}
      >
        <GradientWrapper
          colors={['#667eea', '#764ba2']}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
          style={{
            padding: spacing.lg,
          }}
        >
          <XStack alignItems="center" justifyContent="space-between">
            <YStack flex={1}>
              <XStack alignItems="center" gap="$2" marginBottom="$1">
                <View
                  style={{
                    width: 32,
                    height: 32,
                    borderRadius: 16,
                    backgroundColor: 'rgba(255, 255, 255, 0.2)',
                    justifyContent: 'center',
                    alignItems: 'center',
                  }}
                >
                  <ChartIcon size={18} color="white" />
                </View>
                <Text
                  color="rgba(255, 255, 255, 0.9)"
                  fontSize={14}
                  fontWeight="500"
                  fontFamily="$body"
                >
                  Total Measurements
                </Text>
              </XStack>
              
              <XStack alignItems="baseline" gap="$2">
                <Text
                  color="white"
                  fontSize={32}
                  fontWeight="700"
                  fontFamily="$heading"
                  lineHeight={36}
                >
                  {isLoading ? '...' : totalCount.toLocaleString()}
                </Text>
                <XStack alignItems="center" gap="$1">
                  <TrendingUpIcon size={14} color="rgba(255, 255, 255, 0.8)" />
                  <Text
                    color="rgba(255, 255, 255, 0.8)"
                    fontSize={12}
                    fontWeight="500"
                    fontFamily="$body"
                  >
                    Active
                  </Text>
                </XStack>
              </XStack>
            </YStack>

            <View
              style={{
                width: 60,
                height: 60,
                borderRadius: 30,
                backgroundColor: 'rgba(255, 255, 255, 0.1)',
                justifyContent: 'center',
                alignItems: 'center',
                borderWidth: 2,
                borderColor: 'rgba(255, 255, 255, 0.2)',
              }}
            >
              <Text
                color="white"
                fontSize={24}
                fontWeight="600"
                fontFamily="$heading"
              >
                📋
              </Text>
            </View>
          </XStack>
        </GradientWrapper>

        {/* Bottom section with additional info */}
        <View
          style={{
            backgroundColor: colors.card,
            paddingHorizontal: spacing.lg,
            paddingVertical: spacing.md,
          }}
        >
          <XStack justifyContent="space-between" alignItems="center">
            <YStack>
              <Text
                color={colors.textSecondary}
                fontSize={12}
                fontWeight="500"
                fontFamily="$body"
              >
                Last Updated
              </Text>
              <Text
                color={colors.text}
                fontSize={13}
                fontWeight="600"
                fontFamily="$body"
              >
                {new Date().toLocaleDateString('en-US', {
                  month: 'short',
                  day: 'numeric',
                  hour: '2-digit',
                  minute: '2-digit',
                })}
              </Text>
            </YStack>
            
            <View
              style={{
                backgroundColor: `${colors.success}15`,
                paddingHorizontal: spacing.sm,
                paddingVertical: spacing.xs,
                borderRadius: borderRadius.pill,
                borderWidth: 1,
                borderColor: `${colors.success}20`,
              }}
            >
              <Text
                color={colors.success}
                fontSize={11}
                fontWeight="600"
                fontFamily="$body"
              >
                LIVE DATA
              </Text>
            </View>
          </XStack>
        </View>
      </Card>
    </Animated.View>
  );
};

export default StatisticsCard;
