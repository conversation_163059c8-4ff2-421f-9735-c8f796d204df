import React from 'react';
import { View, Text } from 'react-native';

interface IconProps {
  size?: number;
  color?: string;
  opacity?: number;
  marginRight?: string;
}

export const HomeIcon: React.FC<IconProps> = ({ size = 24, color = 'black' }) => {
  return (
    <View style={{ width: size, height: size, justifyContent: 'center', alignItems: 'center' }}>
      <Text style={{ fontSize: size * 0.8, color }}>🏠</Text>
    </View>
  );
};

export const UserIcon: React.FC<IconProps> = ({ size = 24, color = 'black', opacity = 1 }) => {
  return (
    <View style={{ width: size, height: size, justifyContent: 'center', alignItems: 'center', opacity }}>
      <Text style={{ fontSize: size * 0.8, color }}>👤</Text>
    </View>
  );
};

export const SettingsIcon: React.FC<IconProps> = ({ size = 24, color = 'black' }) => {
  return (
    <View style={{ width: size, height: size, justifyContent: 'center', alignItems: 'center' }}>
      <Text style={{ fontSize: size * 0.8, color }}>⚙️</Text>
    </View>
  );
};

export const SearchIcon: React.FC<IconProps> = ({ size = 24, color = 'black', opacity = 1, marginRight }) => {
  return (
    <View style={{ width: size, height: size, justifyContent: 'center', alignItems: 'center', opacity, marginRight }}>
      <Text style={{ fontSize: size * 0.8, color }}>🔍</Text>
    </View>
  );
};

export const CalendarIcon: React.FC<IconProps> = ({ size = 24, color = 'black', opacity = 1 }) => {
  return (
    <View style={{ width: size, height: size, justifyContent: 'center', alignItems: 'center', opacity }}>
      <Text style={{ fontSize: size * 0.8, color }}>📅</Text>
    </View>
  );
};

export const PhoneIcon: React.FC<IconProps> = ({ size = 24, color = 'black', opacity = 1 }) => {
  return (
    <View style={{ width: size, height: size, justifyContent: 'center', alignItems: 'center', opacity }}>
      <Text style={{ fontSize: size * 0.8, color }}>📱</Text>
    </View>
  );
};

export const FileTextIcon: React.FC<IconProps> = ({ size = 24, color = 'black', opacity = 1 }) => {
  return (
    <View style={{ width: size, height: size, justifyContent: 'center', alignItems: 'center', opacity }}>
      <Text style={{ fontSize: size * 0.8, color }}>📄</Text>
    </View>
  );
};

export const DollarSignIcon: React.FC<IconProps> = ({ size = 24, color = 'black', opacity = 1 }) => {
  return (
    <View style={{ width: size, height: size, justifyContent: 'center', alignItems: 'center', opacity }}>
      <Text style={{ fontSize: size * 0.8, color }}>💲</Text>
    </View>
  );
};

export const FilterIcon: React.FC<IconProps> = ({ size = 24, color = 'black' }) => {
  return (
    <View style={{ width: size, height: size, justifyContent: 'center', alignItems: 'center' }}>
      <Text style={{ fontSize: size * 0.8, color }}>🔍</Text>
    </View>
  );
};

export const MailIcon: React.FC<IconProps> = ({ size = 24, color = 'black' }) => {
  return (
    <View style={{ width: size, height: size, justifyContent: 'center', alignItems: 'center' }}>
      <Text style={{ fontSize: size * 0.8, color }}>✉️</Text>
    </View>
  );
};

export const MapPinIcon: React.FC<IconProps> = ({ size = 24, color = 'black' }) => {
  return (
    <View style={{ width: size, height: size, justifyContent: 'center', alignItems: 'center' }}>
      <Text style={{ fontSize: size * 0.8, color }}>📍</Text>
    </View>
  );
};

export const MoonIcon: React.FC<IconProps> = ({ size = 24, color = 'black' }) => {
  return (
    <View style={{ width: size, height: size, justifyContent: 'center', alignItems: 'center' }}>
      <Text style={{ fontSize: size * 0.8, color }}>🌙</Text>
    </View>
  );
};

export const BellIcon: React.FC<IconProps> = ({ size = 24, color = 'black' }) => {
  return (
    <View style={{ width: size, height: size, justifyContent: 'center', alignItems: 'center' }}>
      <Text style={{ fontSize: size * 0.8, color }}>🔔</Text>
    </View>
  );
};

export const GlobeIcon: React.FC<IconProps> = ({ size = 24, color = 'black' }) => {
  return (
    <View style={{ width: size, height: size, justifyContent: 'center', alignItems: 'center' }}>
      <Text style={{ fontSize: size * 0.8, color }}>🌎</Text>
    </View>
  );
};

export const LockIcon: React.FC<IconProps> = ({ size = 24, color = 'black' }) => {
  return (
    <View style={{ width: size, height: size, justifyContent: 'center', alignItems: 'center' }}>
      <Text style={{ fontSize: size * 0.8, color }}>🔒</Text>
    </View>
  );
};

export const HelpCircleIcon: React.FC<IconProps> = ({ size = 24, color = 'black' }) => {
  return (
    <View style={{ width: size, height: size, justifyContent: 'center', alignItems: 'center' }}>
      <Text style={{ fontSize: size * 0.8, color }}>❓</Text>
    </View>
  );
};

export const PlusIcon: React.FC<IconProps> = ({ size = 24, color = 'white' }) => {
  return (
    <View style={{ width: size, height: size, justifyContent: 'center', alignItems: 'center' }}>
      <Text style={{ fontSize: size * 1.2, color, fontWeight: 'bold' }}>+</Text>
    </View>
  );
};

export const ChartIcon: React.FC<IconProps> = ({ size = 24, color = 'black', opacity = 1 }) => {
  return (
    <View style={{ width: size, height: size, justifyContent: 'center', alignItems: 'center', opacity }}>
      <Text style={{ fontSize: size * 0.8, color }}>📊</Text>
    </View>
  );
};

export const TrendingUpIcon: React.FC<IconProps> = ({ size = 24, color = 'black', opacity = 1 }) => {
  return (
    <View style={{ width: size, height: size, justifyContent: 'center', alignItems: 'center', opacity }}>
      <Text style={{ fontSize: size * 0.8, color }}>📈</Text>
    </View>
  );
};

export const CounterIcon: React.FC<IconProps> = ({ size = 24, color = 'black', opacity = 1 }) => {
  return (
    <View style={{ width: size, height: size, justifyContent: 'center', alignItems: 'center', opacity }}>
      <Text style={{ fontSize: size * 0.8, color }}>🔢</Text>
    </View>
  );
};
