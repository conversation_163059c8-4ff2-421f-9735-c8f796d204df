import React from 'react';
import { ViewStyle } from 'react-native';
import { LinearGradient as ExpoLinearGradient } from 'expo-linear-gradient';

interface GradientWrapperProps {
  colors: string[];
  start?: { x: number; y: number };
  end?: { x: number; y: number };
  style?: ViewStyle;
  children?: React.ReactNode;
}

/**
 * A wrapper component for LinearGradient to fix TypeScript issues
 */
const GradientWrapper: React.FC<GradientWrapperProps> = ({
  colors,
  start = { x: 0, y: 0 },
  end = { x: 1, y: 1 },
  style,
  children,
}) => {
  return (
    <ExpoLinearGradient
      colors={colors}
      start={start}
      end={end}
      style={style}
    >
      {children}
    </ExpoLinearGradient>
  );
};

export default GradientWrapper;
