import React from 'react';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { RootStackParamList } from '../navigation/types';
import { Avatar, Button, H2, Paragraph, XStack, YStack, Card, Text, Separator } from 'tamagui';
import { SettingsIcon, MailIcon, PhoneIcon, MapPinIcon } from '../components/TabIcons';

type Props = NativeStackScreenProps<RootStackParamList, 'Profile'>;

const ProfileScreen = ({ navigation }: Props) => {
  return (
    <YStack f={1} padding="$4" space="$4">
      <YStack alignItems="center" space="$2">
        <Avatar circular size="$10">
          <Avatar.Image src="https://images.unsplash.com/photo-1548142813-c348350df52b?&w=150&h=150&dpr=2&q=80" />
          <Avatar.Fallback backgroundColor="$blue10" />
        </Avatar>
        <H2><PERSON></H2>
        <Paragraph theme="alt2">Tailor</Paragraph>
      </YStack>

      <Separator />

      <Card elevate bordered padding="$4">
        <YStack space="$3">
          <XStack space="$2" alignItems="center">
            <MailIcon size={18} />
            <Text><EMAIL></Text>
          </XStack>

          <XStack space="$2" alignItems="center">
            <PhoneIcon size={18} />
            <Text>+****************</Text>
          </XStack>

          <XStack space="$2" alignItems="center">
            <MapPinIcon size={18} />
            <Text>New York, NY</Text>
          </XStack>
        </YStack>
      </Card>

      <Button
        size="$4"
        theme="active"
        icon={SettingsIcon}
        onPress={() => navigation.navigate('Settings')}
      >
        Go to Settings
      </Button>
    </YStack>
  );
};

export default ProfileScreen;
