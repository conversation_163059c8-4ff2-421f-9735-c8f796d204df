import React from 'react';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { RootStackParamList } from '../navigation/types';
import { Button, H2, XStack, YStack, Text, Switch, Separator, Card } from 'tamagui';
import { HomeIcon, MoonIcon, BellIcon, GlobeIcon, LockIcon, HelpCircleIcon } from '../components/TabIcons';

type Props = NativeStackScreenProps<RootStackParamList, 'Settings'>;

const SettingsScreen = ({ navigation }: Props) => {
  const [isDarkMode, setIsDarkMode] = React.useState(false);
  const [isNotificationsEnabled, setIsNotificationsEnabled] = React.useState(true);
  const [isLocationEnabled, setIsLocationEnabled] = React.useState(false);

  return (
    <YStack f={1} padding="$4" space="$4">
      <H2 textAlign="center">Settings</H2>

      <Card bordered padding="$4">
        <YStack space="$4">
          <XStack justifyContent="space-between" alignItems="center">
            <XStack space="$2" alignItems="center">
              <MoonIcon size={20} />
              <Text>Dark Mode</Text>
            </XStack>
            <Switch
              size="$2"
              checked={isDarkMode}
              onCheckedChange={setIsDarkMode}
            />
          </XStack>

          <Separator />

          <XStack justifyContent="space-between" alignItems="center">
            <XStack space="$2" alignItems="center">
              <BellIcon size={20} />
              <Text>Notifications</Text>
            </XStack>
            <Switch
              size="$2"
              checked={isNotificationsEnabled}
              onCheckedChange={setIsNotificationsEnabled}
            />
          </XStack>

          <Separator />

          <XStack justifyContent="space-between" alignItems="center">
            <XStack space="$2" alignItems="center">
              <GlobeIcon size={20} />
              <Text>Location Services</Text>
            </XStack>
            <Switch
              size="$2"
              checked={isLocationEnabled}
              onCheckedChange={setIsLocationEnabled}
            />
          </XStack>
        </YStack>
      </Card>

      <Card bordered padding="$4">
        <YStack space="$4">
          <XStack space="$2" alignItems="center">
            <LockIcon size={20} />
            <Text>Privacy & Security</Text>
          </XStack>

          <Separator />

          <XStack space="$2" alignItems="center">
            <HelpCircleIcon size={20} />
            <Text>Help & Support</Text>
          </XStack>
        </YStack>
      </Card>

      <Button
        size="$4"
        theme="active"
        icon={HomeIcon}
        onPress={() => navigation.navigate('Home')}
      >
        Go to Home
      </Button>
    </YStack>
  );
};

export default SettingsScreen;
