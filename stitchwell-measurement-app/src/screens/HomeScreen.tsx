import React, { useState, useEffect } from 'react';
import { FlatList, View, ActivityIndicator } from 'react-native';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { RootStackParamList } from '../navigation/types';
import {
  Button,
  H1,
  Paragraph,
  XStack,
  YStack,
  Card,
  Text,
  Input,
  Separator,
} from 'tamagui';
import {
  UserIcon,
  SearchIcon,
  CalendarIcon,
  FileTextIcon,
  FilterIcon
} from '../components/TabIcons';
import GradientWrapper from '../components/GradientWrapper';
import FloatingActionButton from '../components/FloatingActionButton';
import { Measurement } from '../types/index';
import { colors, shadows, borderRadius, spacing } from '../theme/colors';
import { subscribeMeasurements, deleteMeasurement } from '../services/measurementService';

type Props = NativeStackScreenProps<RootStackParamList, 'Home'>;

const HomeScreen = ({ navigation }: Props) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [measurements, setMeasurements] = useState<Measurement[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [loadingMore, setLoadingMore] = useState(false);
  const [allLoaded, setAllLoaded] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Define types for the subscription functions
  type SubscriptionFunctions = {
    unsubscribe: () => void;
    loadMore: () => void;
  };

  // Reference to store the loadMore function
  const measurementsRef = React.useRef<SubscriptionFunctions | null>(null);

  // Handle pull-to-refresh
  const handleRefresh = () => {
    setRefreshing(true);
    // The real-time listener will update the data
    // Just reset the refreshing state after a short delay
    setTimeout(() => {
      setRefreshing(false);
    }, 1000);
  };

  // Handle delete measurement
  const handleDelete = async (id: string) => {
    try {
      await deleteMeasurement(id);
      // No need to update state as the listener will handle it
    } catch (error) {
      console.error('Error deleting measurement:', error);
      setError('Failed to delete measurement. Please try again.');
    }
  };

  // Subscribe to real-time updates from Firestore with pagination
  useEffect(() => {
    setLoading(true);

    // Set up real-time listener with pagination
    const subscription = subscribeMeasurements(
      (updatedMeasurements: Measurement[], paginationInfo: { isLoading: boolean, allLoaded: boolean }) => {
        setMeasurements(updatedMeasurements);
        setLoading(false);
        setLoadingMore(paginationInfo.isLoading);
        setAllLoaded(paginationInfo.allLoaded);
        setError(null);
      },
      15, // pageSize - number of items to load per page
      (error: Error) => {
        console.error('Error in measurements subscription:', error);
        setError('Failed to load measurements. Please try again.');
        setLoading(false);
        setLoadingMore(false);
      }
    ) as SubscriptionFunctions;

    // Store the subscription functions in the ref for later use
    measurementsRef.current = subscription;

    // Cleanup subscription on unmount
    return () => subscription.unsubscribe();
  }, []);

  // Filter measurements based on search query
  const filteredMeasurements = searchQuery.trim() === ''
    ? measurements
    : measurements.filter(measurement =>
        measurement.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        measurement.phoneNo.includes(searchQuery) ||
        measurement.billNo.includes(searchQuery)
      );

  // Render each measurement item
  const renderMeasurementItem = ({ item, index }: { item: Measurement, index: number }) => {
    // Get the first three measurements to display as preview
    const measurementEntries = item.measurements
      ? Object.entries(item.measurements)
          .filter(([_, value]) => value !== undefined && value !== null)
          .slice(0, 3)
      : [];

    // Choose a gradient based on the index (cycle through 3 gradients)
    const gradientKey = `cardGradient${(index % 3) + 1}` as keyof typeof colors;
    const cardGradient = colors[gradientKey] as string[];

    return (
      <Card
        elevate
        size="$4"
        marginVertical="$3"
        scale={0.98}
        hoverStyle={{ scale: 1 }}
        pressStyle={{ scale: 0.96 }}
        onPress={() => {
          // Navigate to detail view when card is pressed
          // This will be implemented in the future
          console.log('Navigate to measurement detail:', item.id);
        }}
        style={{
          backgroundColor: colors.card,
          borderRadius: borderRadius.card,
          overflow: 'hidden',
          ...shadows.medium,
        }}
      >
        <GradientWrapper
          colors={cardGradient}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 0 }}
          style={{
            paddingVertical: spacing.sm,
            paddingHorizontal: spacing.md,
          }}
        >
          <XStack justifyContent="space-between" alignItems="center">
            <XStack gap="$2" alignItems="center">
              <FileTextIcon size={20} color="white" />
              <Text fontWeight="bold" color="white" fontSize={16}>
                {item.billNo ? `Bill #${item.billNo}` : `Measurement #${item.id.substring(0, 6)}`}
              </Text>
            </XStack>
            <Button
              size="$2"
              chromeless
              circular
              onPress={(e) => {
                e.stopPropagation(); // Prevent card onPress from firing
                handleDelete(item.id);
              }}
              pressStyle={{ opacity: 0.5 }}
              hoverStyle={{ opacity: 0.8 }}
            >
              <Text color="white" fontWeight="bold">Delete</Text>
            </Button>
          </XStack>
        </GradientWrapper>

        <YStack padding="$4" gap="$3">
          <XStack gap="$3" alignItems="center">
            <View style={{
              width: 36,
              height: 36,
              borderRadius: 18,
              backgroundColor: `${cardGradient[0]}15`, // Very light tint of the gradient color
              justifyContent: 'center',
              alignItems: 'center',
            }}>
              <UserIcon size={18} color={cardGradient[0]} />
            </View>
            <YStack>
              <Text color={colors.text} fontWeight="600" fontSize={16}>{item.name}</Text>
              <Text color={colors.textSecondary} fontSize={14}>{item.phoneNo}</Text>
            </YStack>
          </XStack>

          <XStack gap="$3" alignItems="center">
            <View style={{
              width: 36,
              height: 36,
              borderRadius: 18,
              backgroundColor: `${cardGradient[0]}15`,
              justifyContent: 'center',
              alignItems: 'center',
            }}>
              <CalendarIcon size={18} color={cardGradient[0]} />
            </View>
            <YStack>
              <Text color={colors.textSecondary} fontSize={14}>Order: {item.date}</Text>
              {item.deliveryDate && (
                <Text color={colors.textSecondary} fontSize={12}>Delivery: {item.deliveryDate}</Text>
              )}
            </YStack>
          </XStack>

          {item.billNo && (
            <XStack gap="$3" alignItems="center">
              <View style={{
                width: 36,
                height: 36,
                borderRadius: 18,
                backgroundColor: `${cardGradient[0]}15`,
                justifyContent: 'center',
                alignItems: 'center',
              }}>
                <FileTextIcon size={18} color={cardGradient[0]} />
              </View>
              <Text color={colors.textSecondary} fontSize={14}>Bill #{item.billNo}</Text>
            </XStack>
          )}

          <Separator />

          <Text fontWeight="600" color={colors.textSecondary} fontSize={14} marginTop="$1">
            Measurements
          </Text>

          <XStack flexWrap="wrap" gap="$2">
            {measurementEntries.map(([key, value], idx) => (
              <View
                key={idx}
                style={{
                  backgroundColor: `${cardGradient[0]}10`,
                  paddingHorizontal: spacing.sm,
                  paddingVertical: spacing['1'],
                  borderRadius: borderRadius.tag,
                  borderWidth: 1,
                  borderColor: `${cardGradient[0]}20`,
                }}
              >
                <Text color={cardGradient[0]} fontSize={14} fontWeight="500">
                  {key.charAt(0).toUpperCase() + key.slice(1)}: {value}
                </Text>
              </View>
            ))}
          </XStack>

          {item.notes && (
            <XStack gap="$3" alignItems="center" marginTop="$1">
              <View style={{
                width: 36,
                height: 36,
                borderRadius: 18,
                backgroundColor: `${cardGradient[0]}15`,
                justifyContent: 'center',
                alignItems: 'center',
              }}>
                <FileTextIcon size={18} color={cardGradient[0]} />
              </View>
              <Text color={colors.textSecondary} numberOfLines={1} ellipsizeMode="tail">
                {item.notes}
              </Text>
            </XStack>
          )}
        </YStack>
      </Card>
    );
  };

  return (
    <YStack f={1} style={{ backgroundColor: colors.background }}>
      <GradientWrapper
        colors={colors.primaryGradient}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
        style={{
          paddingHorizontal: spacing.lg,
          paddingTop: spacing.xl,
          paddingBottom: spacing.lg,
          borderBottomLeftRadius: borderRadius.medium,
          borderBottomRightRadius: borderRadius.medium,
          ...shadows.medium,
        }}
      >
        <H1
          marginBottom="$1"
          color={colors.textLight}
          fontSize={32}
          fontWeight="bold"
        >
          Measurements
        </H1>
        <Paragraph
          marginBottom="$4"
          color="rgba(255, 255, 255, 0.8)"
          fontSize={16}
        >
          View and manage customer measurement records
        </Paragraph>

        <XStack gap="$3" marginBottom="$2">
          <Input
            flex={1}
            placeholder="Search by name, phone, or bill number"
            value={searchQuery}
            onChangeText={setSearchQuery}
            inputMode="text"
            borderColor="transparent"
            backgroundColor="rgba(255, 255, 255, 0.15)"
            placeholderTextColor="rgba(255, 255, 255, 0.6)"
            color={colors.textLight}
            style={{
              borderRadius: borderRadius.pill,
              height: 48,
              paddingHorizontal: spacing.md,
              ...shadows.small,
            }}
          >
            <SearchIcon size={18} color="rgba(255, 255, 255, 0.8)" marginRight="$2" />
          </Input>
          <Button
            icon={FilterIcon}
            circular
            size="$4"
            backgroundColor="rgba(255, 255, 255, 0.15)"
            borderColor="transparent"
            hoverStyle={{ backgroundColor: 'rgba(255, 255, 255, 0.25)' }}
            pressStyle={{ backgroundColor: 'rgba(255, 255, 255, 0.1)' }}
            style={{
              width: 48,
              height: 48,
              ...shadows.small,
            }}
          />
        </XStack>
      </GradientWrapper>

      {loading ? (
        <YStack flex={1} justifyContent="center" alignItems="center" padding="$6">
          <ActivityIndicator size="large" color={colors.primary} />
          <Text color={colors.textSecondary} marginTop="$4" fontSize={16}>
            Loading measurements...
          </Text>
        </YStack>
      ) : error ? (
        <YStack
          alignItems="center"
          padding="$8"
          marginTop="$8"
          marginHorizontal={spacing.lg}
          backgroundColor={colors.card}
          borderRadius={borderRadius.card}
          style={{
            ...shadows.medium,
            borderWidth: 1,
            borderColor: `${colors.error}20`,
          }}
        >
          <View
            style={{
              width: 60,
              height: 60,
              borderRadius: 30,
              backgroundColor: `${colors.error}15`,
              justifyContent: 'center',
              alignItems: 'center',
              marginBottom: spacing.md,
            }}
          >
            <Text fontSize={24}>⚠️</Text>
          </View>
          <Text color={colors.text} fontSize={18} fontWeight="600" marginBottom="$2">
            Something went wrong
          </Text>
          <Text color={colors.textSecondary} textAlign="center" marginBottom="$4">
            {error}
          </Text>
          <Button
            marginTop="$4"
            backgroundColor={colors.primary}
            color={colors.textLight}
            borderRadius={borderRadius.button}
            paddingHorizontal={spacing.lg}
            height={48}
            onPress={() => {
              setLoading(true);
              setError(null);
              setAllLoaded(false);
              setLoadingMore(false);

              // Re-initialize the subscription with pagination
              const subscription = subscribeMeasurements(
                (updatedMeasurements: Measurement[], paginationInfo: { isLoading: boolean, allLoaded: boolean }) => {
                  setMeasurements(updatedMeasurements);
                  setLoading(false);
                  setLoadingMore(paginationInfo.isLoading);
                  setAllLoaded(paginationInfo.allLoaded);
                  setError(null);
                },
                15, // pageSize
                (error: Error) => {
                  console.error('Error in measurements subscription:', error);
                  setError('Failed to load measurements. Please try again.');
                  setLoading(false);
                  setLoadingMore(false);
                }
              ) as SubscriptionFunctions;

              // Update the ref
              measurementsRef.current = subscription;
            }}
          >
            Try Again
          </Button>
        </YStack>
      ) : (
        <FlatList
          data={filteredMeasurements}
          renderItem={renderMeasurementItem}
          keyExtractor={item => item.id}
          showsVerticalScrollIndicator={false}
          refreshing={refreshing}
          onRefresh={handleRefresh}
          onEndReached={() => {
            if (!loadingMore && !allLoaded && measurementsRef.current) {
              measurementsRef.current.loadMore();
            }
          }}
          onEndReachedThreshold={0.5} // Trigger when user scrolls to 50% of the end
          contentContainerStyle={{
            paddingHorizontal: spacing.lg,
            paddingTop: spacing.md,
            paddingBottom: spacing.xxl * 2,
          }}
          ListFooterComponent={
            loadingMore ? (
              <YStack padding="$6" alignItems="center">
                <ActivityIndicator size="small" color={colors.primary} />
                <Text marginTop="$2" color={colors.textSecondary} fontSize={14}>
                  Loading more measurements...
                </Text>
              </YStack>
            ) : allLoaded && measurements.length > 0 ? (
              <YStack padding="$6" alignItems="center">
                <View
                  style={{
                    width: 40,
                    height: 40,
                    borderRadius: 20,
                    backgroundColor: `${colors.primary}10`,
                    justifyContent: 'center',
                    alignItems: 'center',
                    marginBottom: spacing.sm,
                  }}
                >
                  <Text fontSize={18}>✓</Text>
                </View>
                <Text color={colors.textSecondary} fontSize={14}>
                  You've reached the end
                </Text>
              </YStack>
            ) : null
          }
          ListEmptyComponent={
            <YStack
              alignItems="center"
              padding="$8"
              marginTop="$8"
              marginHorizontal={spacing.lg}
              backgroundColor={colors.card}
              borderRadius={borderRadius.card}
              style={{
                ...shadows.medium,
                borderWidth: 1,
                borderColor: colors.border,
              }}
            >
              <View
                style={{
                  width: 60,
                  height: 60,
                  borderRadius: 30,
                  backgroundColor: `${colors.primary}10`,
                  justifyContent: 'center',
                  alignItems: 'center',
                  marginBottom: spacing.md,
                }}
              >
                <Text fontSize={24}>📋</Text>
              </View>
              <Text color={colors.text} fontSize={18} fontWeight="600" marginBottom="$2">
                No measurements yet
              </Text>
              <Text color={colors.textSecondary} textAlign="center">
                Add your first measurement by tapping the + button
              </Text>
            </YStack>
          }
        />
      )}

      <FloatingActionButton
        onPress={() => {
          // For now, we'll navigate to the existing AddBilling screen
          // In the future, this should be updated to navigate to an AddMeasurement screen
          navigation.navigate('AddBilling');
        }}
      />
    </YStack>
  );
};

export default HomeScreen;
