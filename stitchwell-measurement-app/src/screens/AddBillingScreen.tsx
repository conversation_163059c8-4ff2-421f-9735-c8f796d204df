import React, { useState } from 'react';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { RootStackParamList } from '../navigation/types';
import {
  Button,
  H2,
  XStack,
  YStack,
  Input,
  Text,
  Separator,
  Form,
  Select,
} from 'tamagui';
import { KeyboardAvoidingView, Platform, ScrollView, View, StyleSheet } from 'react-native';
import { BillingRecord } from '../types';
import { colors, shadows, borderRadius, spacing } from '../theme/colors';

type Props = NativeStackScreenProps<RootStackParamList, 'AddBilling'>;

const AddBillingScreen = ({ navigation, route }: Props) => {
  const [customerName, setCustomerName] = useState('');
  const [contactNumber, setContactNumber] = useState('');
  const [amount, setAmount] = useState('');
  const [status, setStatus] = useState<'paid' | 'pending' | 'overdue'>('pending');

  const handleSubmit = () => {
    // Generate a new billing record
    const newRecord: Partial<BillingRecord> = {
      customerName,
      contactNumber,
      amount: parseFloat(amount) || 0,
      status,
      date: new Date().toISOString().split('T')[0],
      billNumber: `BILL-${Date.now().toString().substring(5)}`,
    };

    // Here you would typically save this to your database or state
    console.log('New billing record:', newRecord);

    // Navigate back to the home screen
    navigation.goBack();
  };

  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      style={{ flex: 1, backgroundColor: colors.background }}
    >
      <ScrollView>
        <View style={{
          paddingHorizontal: spacing.lg,
          paddingTop: spacing.lg,
          paddingBottom: spacing.md,
          backgroundColor: colors.card,
          ...shadows.small,
          borderBottomWidth: 1,
          borderBottomColor: colors.border,
        }}>
          <H2
            marginBottom="$2"
            color={colors.primary}
            fontSize={24}
          >
            Add New Billing Record
          </H2>
        </View>

        <YStack padding={spacing.lg} gap={spacing.lg}>
          <Form onSubmit={handleSubmit}>
            <YStack gap={spacing.lg}>
              <View style={styles.formGroup}>
                <Text style={styles.label}>Customer Name</Text>
                <Input
                  size="$4"
                  value={customerName}
                  onChangeText={setCustomerName}
                  placeholder="Enter customer name"
                  borderColor={colors.border}
                  style={styles.input}
                />
              </View>

              <View style={styles.formGroup}>
                <Text style={styles.label}>Contact Number</Text>
                <Input
                  size="$4"
                  value={contactNumber}
                  onChangeText={setContactNumber}
                  placeholder="Enter contact number"
                  keyboardType="phone-pad"
                  borderColor={colors.border}
                  style={styles.input}
                />
              </View>

              <View style={styles.formGroup}>
                <Text style={styles.label}>Amount ($)</Text>
                <Input
                  size="$4"
                  value={amount}
                  onChangeText={setAmount}
                  placeholder="Enter amount"
                  keyboardType="numeric"
                  borderColor={colors.border}
                  style={styles.input}
                />
              </View>

              <View style={styles.formGroup}>
                <Text style={styles.label}>Status</Text>
                <Select
                  value={status}
                  onValueChange={(val) => setStatus(val as 'paid' | 'pending' | 'overdue')}
                  size="$4"
                >
                  <Select.Trigger style={styles.select}>
                    <Select.Value placeholder="Select status" />
                  </Select.Trigger>
                  <Select.Content>
                    <Select.Item index={0} value="paid">
                      <Select.ItemText>Paid</Select.ItemText>
                    </Select.Item>
                    <Select.Item index={1} value="pending">
                      <Select.ItemText>Pending</Select.ItemText>
                    </Select.Item>
                    <Select.Item index={2} value="overdue">
                      <Select.ItemText>Overdue</Select.ItemText>
                    </Select.Item>
                  </Select.Content>
                </Select>
              </View>

              <XStack gap={spacing.md} justifyContent="flex-end" marginTop={spacing.md}>
                <Button
                  size="$4"
                  backgroundColor={colors.background}
                  color={colors.textSecondary}
                  borderColor={colors.border}
                  borderWidth={1}
                  style={{
                    borderRadius: borderRadius.medium,
                  }}
                  onPress={() => navigation.goBack()}
                >
                  Cancel
                </Button>
                <Button
                  size="$4"
                  backgroundColor={colors.primary}
                  style={{
                    borderRadius: borderRadius.medium,
                    ...shadows.small,
                  }}
                  onPress={handleSubmit}
                >
                  Save
                </Button>
              </XStack>
            </YStack>
          </Form>
        </YStack>
      </ScrollView>
    </KeyboardAvoidingView>
  );
};

const styles = StyleSheet.create({
  formGroup: {
    marginBottom: spacing.sm,
  },
  label: {
    fontSize: 16,
    marginBottom: 8,
    color: colors.text,
    fontWeight: '500',
  },
  input: {
    borderRadius: borderRadius.medium,
    backgroundColor: colors.background,
  },
  select: {
    borderRadius: borderRadius.medium,
    borderColor: colors.border,
    backgroundColor: colors.background,
  }
});

export default AddBillingScreen;
