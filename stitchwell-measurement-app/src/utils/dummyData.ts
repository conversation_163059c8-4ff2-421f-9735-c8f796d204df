import { BillingRecord } from '../types/index';

// Function to generate a random date within the last 3 months
const getRandomDate = (): string => {
  const today = new Date();
  const threeMonthsAgo = new Date();
  threeMonthsAgo.setMonth(today.getMonth() - 3);

  const randomTimestamp = threeMonthsAgo.getTime() + Math.random() * (today.getTime() - threeMonthsAgo.getTime());
  const randomDate = new Date(randomTimestamp);

  return randomDate.toISOString().split('T')[0]; // Format as YYYY-MM-DD
};

// Function to generate a random bill number
const generateBillNumber = (index: number): string => {
  return `BILL-${(2023000 + index).toString().padStart(6, '0')}`;
};

// Function to generate a random phone number
const generatePhoneNumber = (): string => {
  const areaCode = Math.floor(Math.random() * 900) + 100;
  const firstPart = Math.floor(Math.random() * 900) + 100;
  const secondPart = Math.floor(Math.random() * 9000) + 1000;

  return `+1 (${areaCode}) ${firstPart}-${secondPart}`;
};

// Array of sample customer names
const customerNames = [
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  'Sophia <PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON> <PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON> <PERSON>',
  '<PERSON> <PERSON>',
  '<PERSON>',
  '<PERSON> <PERSON>',
  '<PERSON> <PERSON>',
  '<PERSON>',
  '<PERSON> <PERSON>',
  'Joshua Lewis',
  'Elizabeth Lee',
  'Christopher Walker'
];

// Function to generate a random amount between $50 and $500
const generateAmount = (): number => {
  return Math.floor(Math.random() * 45000) / 100 + 50; // Between $50 and $500
};

// Function to randomly select a status
const generateStatus = (): 'paid' | 'pending' | 'overdue' => {
  const statuses: ('paid' | 'pending' | 'overdue')[] = ['paid', 'pending', 'overdue'];
  return statuses[Math.floor(Math.random() * statuses.length)];
};

// Generate 20 dummy billing records
export const generateDummyBillingRecords = (): BillingRecord[] => {
  return Array.from({ length: 20 }, (_, index) => ({
    id: (index + 1).toString(),
    billNumber: generateBillNumber(index + 1),
    customerName: customerNames[Math.floor(Math.random() * customerNames.length)],
    contactNumber: generatePhoneNumber(),
    date: getRandomDate(),
    amount: generateAmount(),
    status: generateStatus()
  }));
};

// Export the dummy data
export const dummyBillingRecords = generateDummyBillingRecords();
