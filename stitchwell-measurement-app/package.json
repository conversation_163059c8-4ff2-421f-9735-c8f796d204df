{"name": "stitchwell-measurement-app", "version": "1.0.0", "main": "index.ts", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web"}, "dependencies": {"@react-native-async-storage/async-storage": "2.1.2", "@react-navigation/bottom-tabs": "^7.3.13", "@react-navigation/native": "^7.1.9", "@react-navigation/native-stack": "^7.3.13", "@tamagui/animations-react-native": "^1.126.13", "@tamagui/config": "^1.126.13", "@tamagui/core": "^1.126.13", "@tamagui/font-inter": "^1.126.13", "@tamagui/lucide-icons": "^1.126.13", "@tamagui/react-native-media-driver": "^1.126.13", "@tamagui/shorthands": "^1.126.13", "@tamagui/themes": "^1.126.13", "@tamagui/toast": "^1.126.13", "@tamagui/web": "^1.126.13", "expo": "~53.0.9", "expo-font": "~13.3.1", "expo-linear-gradient": "^14.1.4", "expo-status-bar": "~2.2.3", "firebase": "^11.8.1", "react": "19.0.0", "react-dom": "^19.0.0", "react-native": "0.79.2", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.10.0", "react-native-web": "^0.20.0", "tamagui": "^1.126.13"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/react": "~19.0.10", "typescript": "~5.8.3"}, "private": true}